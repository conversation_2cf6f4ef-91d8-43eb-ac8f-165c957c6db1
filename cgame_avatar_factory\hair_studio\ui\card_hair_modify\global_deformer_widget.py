#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Global Deformer Widget Module (Refactored).

This module provides the main widget for global deformation controls.
Refactored to use separate component modules for better maintainability.
"""

import logging
from typing import Optional

# Import Qt modules
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.hair_studio.constants import DEFAULT_MARGIN, DEFAULT_SPACING
from cgame_avatar_factory.hair_studio.data.gwrap_data import (
    GlobalDeformer,
    GlobalDeformerManager,
)
from cgame_avatar_factory.hair_studio.ui.card_hair_modify.components import (
    LeftPanelWidget,
    MiddlePanelWidget,
    RightPanelWidget,
)
from cgame_avatar_factory.hair_studio.constants import GLOBAL_DEFORMER_STYLE
class GlobalDeformerWidget(QtWidgets.QWidget):
    """Global Deformer Widget (Refactored).

    This widget provides global deformation controls for hair components.
    It uses separate component modules for better code organization.
    """
    
    # Define signals as class attributes
    deformer_added = QtCore.Signal(GlobalDeformer)
    deformer_removed = QtCore.Signal(str)  # deformer_name
    deformer_selected = QtCore.Signal(str)  # deformer_name
    deformer_updated = QtCore.Signal(GlobalDeformer)

    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Always use this module's own logger to maintain module identity
        self._logger = logging.getLogger(__name__)

        # Initialize data management first
        self.init_data_management()

        # Initialize UI
        self.setup_ui()
        
        self.setStyleSheet(GLOBAL_DEFORMER_STYLE)

    def setup_ui(self):
        """Setup the main UI layout using component modules."""
        # Create main horizontal layout
        main_layout = QtWidgets.QHBoxLayout(self)
        main_layout.setSpacing(DEFAULT_SPACING)
        main_layout.setContentsMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN)

        # Create component panels
        self.left_panel = LeftPanelWidget()
        self.middle_panel = MiddlePanelWidget()
        self.right_panel = RightPanelWidget()

        # Add panels to main layout
        main_layout.addWidget(self.left_panel)
        main_layout.addWidget(self.middle_panel, 1)  # Stretch factor 1 for expansion
        main_layout.addWidget(self.right_panel)

        # Setup signal connections between components
        self.setup_connections()


    def init_data_management(self):
        """Initialize data management for the widget."""
        # Initialize global deformer manager
        self.deformer_manager = GlobalDeformerManager()

        # Set up UI callbacks for data manager
        self.deformer_manager.on_deformer_added = self._on_data_deformer_added
        self.deformer_manager.on_deformer_removed = self._on_data_deformer_removed
        self.deformer_manager.on_deformer_updated = self._on_data_deformer_updated
        self.deformer_manager.on_selection_changed = self._on_data_selection_changed

    def setup_connections(self):
        """Setup signal connections between components."""
        # Left panel connections
        self.left_panel.curve_generated.connect(self.on_curve_generated)
        self.left_panel.hair_object_selected.connect(self.on_hair_object_selected)
        self.left_panel.mesh_object_selected.connect(self.on_mesh_object_selected)

        # Middle panel connections
        self.middle_panel.add_deformer_requested.connect(self.on_add_deformer)
        self.middle_panel.remove_deformer_requested.connect(self.on_remove_deformer)
        self.middle_panel.deformer_selection_changed.connect(self.on_deformer_selection_changed)

        # Right panel connections
        self.right_panel.rebuild_segments_changed.connect(self.on_rebuild_segments_changed)
        self.right_panel.influence_range_changed.connect(self.on_influence_range_changed)
        self.right_panel.affected_faces_selected.connect(self.on_affected_faces_selected)
        self.right_panel.affected_objects_set.connect(self.on_set_affected_objects)

    # Data Manager Callbacks (UI updates from data changes)
    def _on_data_deformer_added(self, deformer: GlobalDeformer):
        """Handle deformer added from data manager."""
        print(f"[UI] Data callback: Deformer added - {deformer.name}")
        
        # Add to middle panel list
        self.middle_panel.add_deformer_to_list(deformer)
        
        # Emit external signal
        self.deformer_added.emit(deformer)

    def _on_data_deformer_removed(self, deformer_name: str):
        """Handle deformer removed from data manager."""
        print(f"[UI] Data callback: Deformer removed - {deformer_name}")
        
        # Remove from middle panel list
        self.middle_panel.remove_deformer_from_list(deformer_name)
        
        # Emit external signal
        self.deformer_removed.emit(deformer_name)

    def _on_data_deformer_updated(self, deformer: GlobalDeformer):
        """Handle deformer updated from data manager."""
        print(f"[UI] Data callback: Deformer updated - {deformer.name}")
        
        # Update right panel if this is the selected deformer
        selected_deformer = self.deformer_manager.get_selected_deformer()
        if selected_deformer and selected_deformer.name == deformer.name:
            self.right_panel.update_deformer_info(deformer)
        
        # Emit external signal
        self.deformer_updated.emit(deformer)

    def _on_data_selection_changed(self, deformer_name: Optional[str]):
        """Handle selection changed from data manager."""
        display_name = deformer_name or "None"
        print(f"[UI] Data callback: Selection changed to - {display_name}")
        
        # Update UI selection
        if deformer_name is None:
            self.middle_panel.clear_selection()
            self.right_panel.update_deformer_info(None)
        else:
            deformer = self.deformer_manager.get_deformer_by_name(deformer_name)
            if deformer:
                # Update middle panel selection
                self.middle_panel.select_deformer_by_name(deformer.name)
                
                # Update right panel info
                self.right_panel.update_deformer_info(deformer)
        
        # Emit external signal
        if deformer_name:
            self.deformer_selected.emit(deformer_name)

    # Event Handlers for component signals
    def on_curve_generated(self, curve_data: str):
        """Handle curve generated from left panel."""
        print(f"[EVENT] Curve generated: {curve_data}")

    def on_hair_object_selected(self, hair_object: str):
        """Handle hair object selected from left panel."""
        print(f"[EVENT] Hair object selected: {hair_object}")

    def on_mesh_object_selected(self, mesh_object: str):
        """Handle mesh object selected from left panel."""
        print(f"[EVENT] Mesh object selected: {mesh_object}")

    def on_add_deformer(self):
        """Handle add deformer request from middle panel."""
        print(f"[EVENT] Add Deformer requested")
        
        # Get data from left panel
        curve_data = self.left_panel.get_generated_curve_data()
        mesh_data = self.left_panel.get_selected_mesh_data()
        
        print(f"[EVENT] Curve data: '{curve_data}'")
        print(f"[EVENT] Mesh data: '{mesh_data}'")
        
        # Validate data
        if not curve_data or curve_data == "未生成曲线":
            print(f"[EVENT] Warning: No curve data available")
            self._logger.warning("请先生成曲线数据")
            return
            
        if not mesh_data or mesh_data == "未选择mesh对象":
            print(f"[EVENT] Warning: No mesh data available")
            self._logger.warning("请先选择mesh对象")
            return
        
        # Create deformer with data from left panel
        deformer = self.deformer_manager.create_deformer(
            curve_data=curve_data,
            binding_mesh=mesh_data
        )
        print(f"[EVENT] Created deformer: {deformer.name}")
        
        # Add via data manager
        success = self.deformer_manager.add_deformer(deformer)
        
        if success:
            print(f"[EVENT] Successfully added deformer")
            self._logger.info(f"Added deformer: {deformer.name}")
        else:
            print(f"[EVENT] Failed to add deformer")
            self._logger.error(f"Failed to add deformer")

    def on_remove_deformer(self):
        """Handle remove deformer request from middle panel."""
        print(f"[EVENT] Remove Deformer requested")
        
        # Get current deformer name from middle panel
        deformer_name = self.middle_panel.get_current_deformer_name()
        if not deformer_name:
            print(f"[EVENT] Warning: No deformer selected for removal")
            self._logger.warning("请先选择要删除的变形器")
            return
        
        print(f"[EVENT] Attempting to remove deformer: {deformer_name}")
        
        # Remove via data manager
        success = self.deformer_manager.remove_deformer(deformer_name)
        if success:
            print(f"[EVENT] Successfully removed deformer: {deformer_name}")
            self._logger.info(f"Removed deformer: {deformer_name}")
        else:
            print(f"[EVENT] Failed to remove deformer: {deformer_name}")
            self._logger.error(f"Failed to remove deformer: {deformer_name}")

    def on_deformer_selection_changed(self, current, previous=None):
        """Handle deformer selection change from middle panel."""
        _ = previous  # Unused parameter
        print(f"[EVENT] Deformer selection changed")
        
        if current is None:
            print(f"[EVENT] No deformer selected")
            self.deformer_manager.select_deformer(None)
            return
        
        # Get deformer name and select via data manager
        deformer_name = current.text()
        print(f"[EVENT] Selected deformer: {deformer_name}")
        
        # Select via data manager
        success = self.deformer_manager.select_deformer(deformer_name)
        if success:
            print(f"[EVENT] Successfully selected deformer: {deformer_name}")
        else:
            print(f"[EVENT] Failed to select deformer: {deformer_name}")

    def on_rebuild_segments_changed(self, value):
        """Handle rebuild segments change from right panel."""
        print(f"[EVENT] Rebuild segments changed to: {value}")
        
        selected_deformer = self.deformer_manager.get_selected_deformer()
        if selected_deformer:
            print(f"[EVENT] Updating rebuild segments for deformer: {selected_deformer.name}")
            self.deformer_manager.update_deformer(selected_deformer.name, rebuild_segments=value)
        else:
            print(f"[EVENT] No deformer selected, ignoring rebuild segments change")

    def on_influence_range_changed(self, value):
        """Handle influence range change from right panel."""
        print(f"[EVENT] Influence range changed to: {value}")
        
        selected_deformer = self.deformer_manager.get_selected_deformer()
        if selected_deformer:
            print(f"[EVENT] Updating influence range for deformer: {selected_deformer.name}")
            self.deformer_manager.update_deformer(selected_deformer.name, influence_range=float(value))
        else:
            print(f"[EVENT] No deformer selected, ignoring influence range change")

    def on_affected_faces_selected(self, faces: str):
        """Handle affected faces selected from right panel."""
        print(f"[EVENT] Affected faces selected: {faces}")

    def on_set_affected_objects(self):
        """Handle set affected objects from right panel."""
        print(f"[EVENT] Set affected objects requested")
        
        selected_deformer = self.deformer_manager.get_selected_deformer()
        if not selected_deformer:
            print(f"[EVENT] Warning: No deformer selected")
            self._logger.warning("请先选择一个变形器")
            return
        
        # This would typically get the objects from the right panel
        # For now, we'll use placeholder logic
        affected_objects = ["hair_object_1", "hair_object_2"]  # Placeholder
        
        print(f"[EVENT] Setting affected objects for deformer: {selected_deformer.name}")
        self.deformer_manager.update_deformer(selected_deformer.name, affected_objects=affected_objects)
        
        self._logger.info(f"Set affected objects for deformer {selected_deformer.name}")

    # Public API methods
    def get_selected_deformer(self) -> Optional[GlobalDeformer]:
        """Get the currently selected deformer."""
        return self.deformer_manager.get_selected_deformer()

    def get_deformer_count(self) -> int:
        """Get the total number of deformers."""
        return len(self.deformer_manager.get_all_deformers())
