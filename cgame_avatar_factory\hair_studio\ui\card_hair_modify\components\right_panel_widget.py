"""Right Panel Widget Module.

This module provides the right panel widget with two groups:
1. Deformer Info Group (2/3 space)
2. Affected Objects Group (1/3 space)
"""

import logging
import random
from typing import Optional

# Import Qt modules
from qtpy import QtCore
from qtpy import QtWidgets

# Import dayu widgets
from dayu_widgets import M<PERSON>abel, MPushButton

# Import local modules
from cgame_avatar_factory.hair_studio.constants import DEFAULT_MARGIN, DEFAULT_SPACING
from cgame_avatar_factory.hair_studio.data.gwrap_data import GlobalDeformer
from cgame_avatar_factory.hair_studio.ui.card_hair_modify.components.property_slider_widget import PropertySliderWidget

class RightPanelWidget(QtWidgets.QWidget):
    """右侧面板组件：变形器信息和受影响对象"""
    
    # 定义信号
    rebuild_segments_changed = QtCore.Signal(int)  # 重建分段变化
    influence_range_changed = QtCore.Signal(int)   # 影响范围变化
    affected_faces_selected = QtCore.Signal(str)   # 选择受影响面片
    affected_objects_set = QtCore.Signal()         # 设置受影响对象
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._logger = logging.getLogger(__name__)
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """设置UI布局"""
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setSpacing(DEFAULT_SPACING)
        main_layout.setContentsMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN)
        
        # 创建两个组，垂直方向2:1比例
        info_group = self.create_deformer_info_section()
        objects_group = self.create_affected_objects_section()
        
        # 添加到主布局，设置拉伸因子
        main_layout.addWidget(info_group, 2)  # 2/3的空间
        main_layout.addWidget(objects_group, 1)  # 1/3的空间
    
    def create_deformer_info_section(self):
        """创建变形器信息区域"""
        info_group = QtWidgets.QGroupBox("变形器信息 Deformer Info")
        info_layout = QtWidgets.QVBoxLayout(info_group)
        info_layout.setSpacing(DEFAULT_SPACING)
        info_layout.setContentsMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN)
        
        # Add initial separator
        separator_start = self._create_separator()
        info_layout.addWidget(separator_start)

        # ===== 第一部分：当前选中属性信息 =====
        info_title = MLabel("当前选中变形器信息：")
        info_layout.addWidget(info_title)

        self.current_deformer_info_label = QtWidgets.QTextEdit()
        self.current_deformer_info_label.setPlaceholderText("未选中变形器--")
        self.current_deformer_info_label.setReadOnly(True)
        self.current_deformer_info_label.setMinimumHeight(100)
        info_layout.addWidget(self.current_deformer_info_label)

        # Add separator after info section
        separator1 = self._create_separator()
        info_layout.addWidget(separator1)

        # ===== 第二部分：具体属性（滑块） =====
        properties_title = MLabel("属性参数：")
        info_layout.addWidget(properties_title)

        # 使用封装的滑条组件
        # 重建分段滑条
        self.rebuild_segments_widget = PropertySliderWidget(
            label_text="重建分段：",
            min_value=1,
            max_value=50,
            default_value=15
        )
        info_layout.addWidget(self.rebuild_segments_widget)

        # 影响范围滑条
        self.influence_range_widget = PropertySliderWidget(
            label_text="影响范围：",
            min_value=1,
            max_value=100,
            default_value=15
        )
        info_layout.addWidget(self.influence_range_widget)

        # Add stretch to push content to top
        info_layout.addStretch()
        
        return info_group
    
    def create_affected_objects_section(self):
        """创建受影响对象区域"""
        objects_group = QtWidgets.QGroupBox("受影响对象 Affected Objects")
        objects_layout = QtWidgets.QVBoxLayout(objects_group)
        objects_layout.setSpacing(DEFAULT_SPACING)
        objects_layout.setContentsMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN)
        
        # Add separator
        separator2 = self._create_separator()
        objects_layout.addWidget(separator2)

        # ===== 受影响面片选择 =====
        # Selection button
        self.select_affected_faces_btn = MPushButton("选择受影响的面片")
        objects_layout.addWidget(self.select_affected_faces_btn)

        # Affected objects area (display selected faces)
        self.affected_objects_area = QtWidgets.QTextEdit()
        self.affected_objects_area.setMaximumHeight(80)
        self.affected_objects_area.setReadOnly(True)
        self.affected_objects_area.setPlaceholderText("受影响的面片将显示在这里...")
        objects_layout.addWidget(self.affected_objects_area)

        # Set affected objects button (keep original functionality)
        self.set_affected_objects_btn = MPushButton("设置影响面片")
        objects_layout.addWidget(self.set_affected_objects_btn)

        # Add stretch to push content to top
        objects_layout.addStretch()
        
        return objects_group
    
    def _create_separator(self):
        """创建分隔线"""
        separator = QtWidgets.QFrame()
        separator.setFrameShape(QtWidgets.QFrame.HLine)
        separator.setFrameShadow(QtWidgets.QFrame.Sunken)
        separator.setStyleSheet("QFrame { color: #cccccc; }")
        return separator
    
    def setup_connections(self):
        """设置信号连接"""
        # 连接滑条组件的信号
        self.rebuild_segments_widget.valueChanged.connect(self.rebuild_segments_changed.emit)
        self.influence_range_widget.valueChanged.connect(self.influence_range_changed.emit)
        
        # 连接按钮信号
        self.select_affected_faces_btn.clicked.connect(self.on_select_affected_faces)
        self.set_affected_objects_btn.clicked.connect(self.affected_objects_set.emit)
    
    def on_select_affected_faces(self):
        """处理选择受影响面片按钮点击"""
        print(f"[EVENT] Select Affected Faces button clicked")
        
        # 模拟获取选中的面片
        selected_faces = self._simulate_maya_selection("faces")
        
        if selected_faces:
            print(f"[EVENT] Affected faces selected: {selected_faces}")
            
            # 更新受影响对象区域
            current_text = self.affected_objects_area.toPlainText()
            if current_text and current_text != "暂无受影响面片...":
                # 追加到现有文本
                new_text = current_text + "\n" + selected_faces
            else:
                # 设置为新文本
                new_text = selected_faces
            
            self.affected_objects_area.setPlainText(new_text)
            self.affected_faces_selected.emit(selected_faces)
            self._logger.info(f"Selected affected faces: {selected_faces}")
        else:
            print(f"[EVENT] No faces selected")
            self._logger.warning("未选择任何面片")

    def _simulate_maya_selection(self, object_type: str) -> str:
        """模拟Maya对象选择"""
        if object_type == "faces":
            test_objects = [
                "hair_mesh_001.f[100:150]",
                "scalp_mesh_A.f[200:250]",
                "hair_geometry_B.f[50:80]",
                "hair_surface_C.f[300:350]",
                "facial_hair_mesh.f[10:30]"
            ]
        else:
            return ""
        
        # 随机选择一个用于测试
        selected = random.choice(test_objects)
        print(f"[MAYA_SIM] Simulated selection of {object_type}: {selected}")
        return selected
    
    def update_deformer_info(self, deformer: Optional[GlobalDeformer]):
        """更新变形器信息显示"""
        print(f"[UI] Updating deformer info panel")

        if deformer is None:
            print(f"[UI] Clearing deformer info panel")
            # 清除信息显示
            self.current_deformer_info_label.setText("无选中变形器")
            self.rebuild_segments_widget.setValue(15)
            self.influence_range_widget.setValue(15)
            self.affected_objects_area.clear()
            return

        print(f"[UI] Updating info for deformer: {deformer.name}")

        # 更新变形器信息显示（第一部分：属性信息）
        info_text = f"驱动名: {deformer.name}\n"
        info_text += f"曲线名: {deformer.curve_data or '未设置'}\n"
        info_text += f"模型名: {deformer.binding_mesh or '未设置'}\n"
        info_text += f"状态: {deformer.status.value}\n"
        info_text += f"创建时间: {deformer.created_time}"
        self.current_deformer_info_label.setText(info_text)

        # 更新属性滑块（第二部分：属性参数）
        self.rebuild_segments_widget.setValue(deformer.rebuild_segments)
        self.influence_range_widget.setValue(int(deformer.influence_range))

        # 更新受影响对象显示
        if deformer.affected_objects:
            print(f"[UI] Affected objects: {deformer.affected_objects}")
            self.affected_objects_area.setPlainText("\n".join(deformer.affected_objects))
        else:
            print(f"[UI] No affected objects")
            self.affected_objects_area.setPlaceholderText("暂无受影响面片...")

        print(f"[UI] Deformer info panel updated successfully")
