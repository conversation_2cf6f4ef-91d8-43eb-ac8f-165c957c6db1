"""Left Panel Widget Module.

This module provides the left panel widget for curve generation and mesh binding.
"""

import logging
import random
from typing import Optional

# Import Qt modules
from qtpy import QtCore
from qtpy import QtWidgets

# Import dayu widgets
from dayu_widgets import <PERSON><PERSON>abel, MLineEdit, MPushButton

# Import local modules
from cgame_avatar_factory.hair_studio.constants import DEFAULT_MARGIN, DEFAULT_SPACING


class LeftPanelWidget(QtWidgets.QWidget):
    """左侧面板组件：曲线生成和mesh绑定"""
    
    # 定义信号
    curve_generated = QtCore.Signal(str)  # 生成的曲线数据
    hair_object_selected = QtCore.Signal(str)  # 选中的毛发对象
    mesh_object_selected = QtCore.Signal(str)  # 选中的mesh对象
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._logger = logging.getLogger(__name__)
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """设置UI布局"""
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setSpacing(DEFAULT_SPACING)
        main_layout.setContentsMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN)
        
        # 创建两个组，垂直方向2:1比例
        curve_group = self.create_curve_generation_section()
        binding_group = self.create_mesh_binding_section()
        
        # 添加到主布局，设置拉伸因子
        main_layout.addWidget(curve_group, 2)  # 2/3的空间
        main_layout.addWidget(binding_group, 1)  # 1/3的空间
    
    def create_curve_generation_section(self):
        """创建曲线生成区域"""
        curve_group = QtWidgets.QGroupBox("生成曲线 Generate Curve")
        curve_layout = QtWidgets.QVBoxLayout(curve_group)
        curve_layout.setSpacing(DEFAULT_SPACING)
        curve_layout.setContentsMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN)
        
        # Hair object selection button
        self.select_hair_btn = MPushButton("请选择毛发面片来生成曲线")
        curve_layout.addWidget(self.select_hair_btn)

        # Hair object display label - shows selected object
        self.hair2curve_label = QtWidgets.QTextEdit()
        self.hair2curve_label.setPlaceholderText("未选择毛发或面片")
        self.hair2curve_label.setReadOnly(True)
        self.hair2curve_label.setMaximumHeight(50)
        curve_layout.addWidget(self.hair2curve_label)

        curve_layout.addStretch()
        
        # Generated curve data display label
        curve_data_title = MLabel("生成的曲线数据：")
        curve_layout.addWidget(curve_data_title)
        
        self.generated_curve_label = QtWidgets.QTextEdit()
        self.generated_curve_label.setPlaceholderText("未生成曲线")
        self.generated_curve_label.setReadOnly(True)
        self.generated_curve_label.setMaximumHeight(50)
        curve_layout.addWidget(self.generated_curve_label)

        # Generate curve button
        self.generate_curve_btn = MPushButton("生成曲线>>")
        curve_layout.addWidget(self.generate_curve_btn, alignment=QtCore.Qt.AlignRight)
        
        # Add stretch to push content to top
        curve_layout.addStretch()
        
        return curve_group
    
    def create_mesh_binding_section(self):
        """创建mesh绑定区域"""
        binding_group = QtWidgets.QGroupBox("创建约束 Create Binding")
        binding_layout = QtWidgets.QVBoxLayout(binding_group)
        binding_layout.setSpacing(DEFAULT_SPACING)
        binding_layout.setContentsMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN)
        
        # Mesh object selection button
        self.select_mesh_btn = MPushButton("请选择头发mesh对象")
        binding_layout.addWidget(self.select_mesh_btn)

        # Mesh object display label - shows selected object
        self.mesh_selection_label = QtWidgets.QTextEdit()
        self.mesh_selection_label.setPlaceholderText("未选择mesh对象")
        self.mesh_selection_label.setReadOnly(True)
        self.mesh_selection_label.setMaximumHeight(50)
        binding_layout.addWidget(self.mesh_selection_label)

        # Binding button
        self.binding_btn = MPushButton("创建驱动>>")
        binding_layout.addWidget(self.binding_btn, alignment= QtCore.Qt.AlignRight)
        
        # Add stretch to push content to top
        binding_layout.addStretch()
        
        return binding_group
    
    def setup_connections(self):
        """设置信号连接"""
        self.select_hair_btn.clicked.connect(self.on_select_hair_object)
        self.select_mesh_btn.clicked.connect(self.on_select_mesh_object)
        self.generate_curve_btn.clicked.connect(self.on_generate_curve)
    
    def on_select_hair_object(self):
        """处理选择毛发对象按钮点击"""
        print(f"[EVENT] Select Hair Object button clicked")
        
        # 模拟Maya选择
        selected_hair_object = self._simulate_maya_selection("hair_curve")
        
        if selected_hair_object:
            print(f"[EVENT] Hair object selected: {selected_hair_object}")
            self.hair2curve_label.setText(selected_hair_object)
            self.hair_object_selected.emit(selected_hair_object)
            self._logger.info(f"Selected hair object: {selected_hair_object}")
        else:
            print(f"[EVENT] No hair object selected")
            self._logger.warning("未选择任何毛发对象")

    def on_select_mesh_object(self):
        """处理选择mesh对象按钮点击"""
        print(f"[EVENT] Select Mesh Object button clicked")
        
        # 模拟Maya选择
        selected_mesh_object = self._simulate_maya_selection("hair_mesh")
        
        if selected_mesh_object:
            print(f"[EVENT] Mesh object selected: {selected_mesh_object}")
            self.mesh_selection_label.setText(selected_mesh_object)
            self.mesh_object_selected.emit(selected_mesh_object)
            self._logger.info(f"Selected mesh object: {selected_mesh_object}")
        else:
            print(f"[EVENT] No mesh object selected")
            self._logger.warning("未选择任何mesh对象")

    def on_generate_curve(self):
        """处理生成曲线按钮点击"""
        print(f"[EVENT] Generate Curve button clicked")
        
        # 获取选中的毛发对象
        hair_object = self.hair2curve_label.text().strip()
        print(f"[EVENT] Hair object from label: '{hair_object}'")
        
        if not hair_object or hair_object == "未选择毛发对象":
            print(f"[EVENT] Warning: No hair object selected")
            self._logger.warning("请先选择毛发对象")
            return
        
        # 生成曲线数据
        generated_curve_data = self._generate_curve_from_hair_object(hair_object)
        print(f"[EVENT] Generated curve data: {generated_curve_data}")
        
        # 更新显示标签
        self.generated_curve_label.setText(generated_curve_data)
        
        # 发射信号
        self.curve_generated.emit(generated_curve_data)
        
        print(f"[EVENT] Successfully generated curve data from: {hair_object}")
        self._logger.info(f"Generated curve data from: {hair_object}")

    def _generate_curve_from_hair_object(self, hair_object: str) -> str:
        """从毛发对象生成曲线数据（模拟）"""
        curve_id = random.randint(1000, 9999)
        generated_curve = f"generated_curve_{curve_id}_from_{hair_object}"
        
        print(f"[CURVE_GEN] Simulated curve generation: {hair_object} -> {generated_curve}")
        return generated_curve

    def _simulate_maya_selection(self, object_type: str) -> str:
        """模拟Maya对象选择"""
        if object_type == "hair_curve":
            test_objects = [
                "hair_curve_001",
                "hair_curve_002", 
                "hair_curve_003",
                "scalp_hair_curve_A",
                "facial_hair_curve_B"
            ]
        elif object_type == "hair_mesh":
            test_objects = [
                "hair_mesh_001",
                "hair_mesh_002",
                "scalp_mesh_A",
                "hair_geometry_B",
                "hair_surface_C"
            ]
        else:
            return ""
        
        # 随机选择一个用于测试
        selected = random.choice(test_objects)
        print(f"[MAYA_SIM] Simulated selection of {object_type}: {selected}")
        return selected
    
    def get_generated_curve_data(self) -> str:
        """获取生成的曲线数据"""
        return self.generated_curve_label.text()
    
    def get_selected_mesh_data(self) -> str:
        """获取选中的mesh数据"""
        return self.mesh_selection_label.text()
